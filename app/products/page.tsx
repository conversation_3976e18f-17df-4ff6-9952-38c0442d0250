'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Cart } from '@/components/Cart';
import { useCart } from '@/components/CartProvider';
import { ShoppingCart, Heart, Package, CheckCircle, XCircle, Plus } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  category: string;
  description: string;
  image: string;
  inStock: boolean;
  features: string[];
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { addToCart, getTotalItems } = useCart();

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/data/products.json');
        const data = await response.json();
        setProducts(data);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const categories = ['All', ...Array.from(new Set(products.map(product => product.category)))];
  const filteredProducts = selectedCategory === 'All'
    ? products
    : products.filter(product => product.category === selectedCategory);

  const handleAddToCart = (product: Product) => {
    addToCart(product);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-emerald-50">
        <Header />
        <div className="pt-20 pb-16 px-2 sm:px-4 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading products...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-emerald-50">
      <Header />

      {/* Floating Cart Button */}
      <div className="fixed bottom-6 right-6 z-40">
        <Button
          onClick={() => setIsCartOpen(true)}
          className="bg-cvets-primary hover:bg-blue-600 text-white rounded-full w-16 h-16 shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div className="relative">
            <ShoppingCart className="w-6 h-6" />
            {getTotalItems() > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {getTotalItems()}
              </span>
            )}
          </div>
        </Button>
      </div>

      {/* Cart Modal */}
      <Cart isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-2 sm:px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <div className="space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center gap-2 bg-cvets-primary/10 text-cvets-primary px-4 py-2 rounded-full text-sm font-medium border border-cvets-gold/30">
                <Package className="w-4 h-4" />
                Quality Veterinary Products
              </div>
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Best Products for
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-cvets-primary to-cvets-purple"> Your Pet's Health</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
                Discover our carefully selected range of veterinary products designed to keep your pets healthy, happy, and comfortable.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 px-2 sm:px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className={`rounded-full px-6 py-2 transition-all duration-300 border ${
                  selectedCategory === category
                    ? 'bg-cvets-primary text-white border-cvets-gold'
                    : 'border-gray-200 text-gray-700 hover:bg-cvets-primary/10 hover:border-cvets-primary/30'
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-16 px-2 sm:px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredProducts.map((product) => (
              <Card
                key={product.id}
                className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border border-cvets-gold/20 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-2xl"
              >
                {/* Large Image - 80-90% of card */}
                <div className="relative h-64 overflow-hidden rounded-t-2xl">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>

                  {/* Stock Badge */}
                  <div className="absolute top-3 right-3">
                    {product.inStock ? (
                      <Badge className="bg-green-100 text-green-800 border-green-200 shadow-sm">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        In Stock
                      </Badge>
                    ) : (
                      <Badge className="bg-red-100 text-red-800 border-red-200 shadow-sm">
                        <XCircle className="w-3 h-3 mr-1" />
                        Out of Stock
                      </Badge>
                    )}
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-3 left-3">
                    <Badge className="bg-cvets-primary/90 text-white border-cvets-gold shadow-sm">
                      {product.category}
                    </Badge>
                  </div>

                  {/* Product Name Overlay */}
                  <div className="absolute bottom-3 left-3 right-3">
                    <h3 className="text-white font-bold text-lg drop-shadow-lg">
                      {product.name}
                    </h3>
                  </div>

                  {/* Hover Description Overlay */}
                  <div className="absolute inset-0 bg-black/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-center p-4">
                    <div className="text-white space-y-3">
                      <h3 className="font-bold text-lg text-cvets-gold">{product.name}</h3>
                      <p className="text-sm leading-relaxed">{product.description}</p>
                      <div className="space-y-1">
                        <h4 className="font-semibold text-cvets-gold text-sm">Features:</h4>
                        <ul className="space-y-1">
                          {product.features.slice(0, 3).map((feature, index) => (
                            <li key={index} className="flex items-center space-x-2 text-xs">
                              <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Compact Bottom Section */}
                <CardContent className="p-4">
                  <Button
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.inStock}
                    className={`w-full rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ${
                      product.inStock
                        ? 'bg-cvets-purple hover:bg-purple-700 text-white border border-cvets-gold/30'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {product.inStock ? (
                      <span className="flex items-center space-x-2">
                        <Plus className="w-4 h-4" />
                        <span>Add to Cart</span>
                      </span>
                    ) : (
                      <span>Out of Stock</span>
                    )}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-cvets-primary via-cvets-purple to-cvets-primary"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Need Help Choosing
              <br />
              <span className="bg-gradient-to-r from-cvets-gold to-yellow-300 bg-clip-text text-transparent">
                The Right Product?
              </span>
            </h2>

            <p className="text-xl text-blue-100 leading-relaxed">
              Contact our team for personalized product recommendations for your pet's specific needs
            </p>

            <Button
              onClick={() => {
                const message = "Hi! I need help choosing the right products for my pet. Could you please provide some recommendations?";
                const whatsappUrl = `https://wa.me/254718376311?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');
              }}
              className="bg-white text-cvets-primary hover:bg-cvets-primary/10 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold border border-cvets-gold/30"
            >
              <span className="flex items-center space-x-2">
                <Heart className="w-5 h-5" />
                <span>Get Expert Advice</span>
              </span>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

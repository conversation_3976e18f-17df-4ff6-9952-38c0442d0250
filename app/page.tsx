'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Phone, Clock, Home, Calendar, Heart, Shield, Star, ArrowRight, Stethoscope, Scissors, Zap, Activity, Siren, Umbrella, Bug, Plane, Sparkles } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAppointment } from '@/components/AppointmentProvider';
import { smoothScrollToElement } from '@/lib/utils';

export default function HomePage() {
  const { openAppointmentModal } = useAppointment();

  // Handle smooth scrolling when page loads with hash
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const elementId = hash.substring(1); // Remove the # symbol
      setTimeout(() => {
        smoothScrollToElement(elementId);
      }, 100);
    }
  }, []);

  const services = [
    {
      title: "Vaccinations",
      description: "Essential vaccination programs to protect your pet from common diseases and maintain their immunity.",
      image: "data:image/jpeg;base64,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",
      icon: Shield
    },
    {
      title: "Health Check-ups",
      description: "Comprehensive health examinations to ensure your pet's optimal wellbeing and early detection of health issues.",
      image: "data:image/jpeg;base64,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",
      icon: Stethoscope
    },
    {
      title: "Surgical Procedures",
      description: "Professional surgical services including spaying, neutering, and other necessary procedures.",
      image: "data:image/jpeg;base64,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",
      icon: Scissors
    },
    {
      title: "Dental Care",
      description: "Complete dental health services including cleaning, extractions, and oral health maintenance.",
      image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTSkWGsUio1jz-kAZnUPybRlHi0-E-BlKkBWw&s",
      icon: Sparkles
    },
    {
      title: "Diagnostic Testing",
      description: "Advanced diagnostic services including blood work, X-rays, and laboratory testing.",
      image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQXdvM3gjwO7trXT51wCPXV91fUJg1OPrSpAQ&s",
      icon: Activity
    },
    {
      title: "Emergency Care",
      description: "24/7 emergency veterinary services for urgent medical situations and critical care.",
      image: "data:image/jpeg;base64,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",
      icon: Siren
    },
    {
      title: "Preventative Care",
      description: "Comprehensive preventive treatments to keep your pets healthy and prevent future health issues.",
      image: "data:image/jpeg;base64,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",
      icon: Shield
    },
    {
      title: "Parasite Control",
      description: "Comprehensive parasite prevention and treatment programs for fleas, ticks, and worms.",
      image: "data:image/jpeg;base64,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",
      icon: Bug
    },
    {
      title: "Wash & Grooming",
      description: "Professional grooming services including bathing, nail trimming, and coat care.",
      image: "data:image/jpeg;base64,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",
      icon: Sparkles
    }
  ];

  const features = [
    {
      icon: Home,
      title: "Home Visits",
      description: "No stressful car rides or waiting rooms. We come to you.",
      gradient: "from-blue-100 to-blue-200"
    },
    {
      icon: Calendar,
      title: "Flexible Scheduling",
      description: "Book appointments that fit your busy schedule.",
      gradient: "from-pink-100 to-pink-200"
    },
    {
      icon: Phone,
      title: "24/7 Support",
      description: "Emergency care available around the clock.",
      gradient: "from-gray-100 to-gray-200"
    },
    {
      icon: Clock,
      title: "Quick Response",
      description: "Fast response times for urgent situations.",
      gradient: "from-blue-100 to-pink-100"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-10 pb-16 px-2 sm:px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"></div>
        <div className="relative max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 bg-cvets-primary/10 text-cvets-primary px-4 py-2 rounded-full text-sm font-medium border border-cvets-gold/30">
                  <Heart className="w-4 h-4" />
                  Professional Veterinary Care
                </div>
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Your Pet's Health is Our
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-cvets-primary to-cvets-purple"> Priority</span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  CVETS Veterinary Services provides comprehensive, compassionate care for your beloved pets.
                  We offer professional veterinary services bringing quality care directly to your home.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6">
                <Button
                  onClick={openAppointmentModal}
                  size="lg"
                  className="bg-cvets-purple hover:bg-purple-700 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-cvets-gold/30"
                >
                  <span>Book Appointment</span>
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 border-gray-200 bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-gray-50 hover:border-gray-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <Link href="/services">View Services</Link>
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-pink-400 rounded-3xl transform rotate-3 opacity-20"></div>
              <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=600&h=400"
                  alt="Veterinarian examining a pet"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-pink-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-2 sm:px-4 lg:px-6">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-pink-100 rounded-2xl px-8 py-4">
              <Shield className="w-6 h-6 text-blue-600" />
              <span className="text-lg font-semibold text-gray-700">Why Choose Us</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent leading-tight">
              Why Choose Mobile
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-pink-600 bg-clip-text text-transparent">
                Veterinary Care?
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the convenience and comfort of professional veterinary services
              delivered directly to your home with unprecedented care and attention.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <CardContent className="p-8 text-center space-y-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-10 h-10 text-slate-700" />
                  </div>

                  <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-slate-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100/50 via-blue-50/50 to-pink-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-2 sm:px-4 lg:px-6">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-pink-100 to-blue-100 rounded-2xl px-8 py-4">
              <Heart className="w-6 h-6 text-pink-600" />
              <span className="text-lg font-semibold text-gray-700">Our Services</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-pink-900 to-gray-900 bg-clip-text text-transparent leading-tight">
              Comprehensive Care
              <br />
              <span className="bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent">
                Tailored to Your Pet
              </span>
            </h2>

            <p className="text-xl text-gray-600 leading-relaxed">
              Professional veterinary services designed with your pet's comfort and health in mind
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <div className="relative h-48 overflow-hidden rounded-t-3xl">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                    <service.icon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>

                <CardContent className="p-8 space-y-4">
                  <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </h3>

                  <p className="text-slate-600 leading-relaxed">
                    {service.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-16">
            <Button
              asChild
              size="lg"
              className="bg-gradient-to-r from-pink-600 to-blue-700 hover:from-pink-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <Link href="/services" className="flex items-center space-x-2">
                <span>View All Services</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Ready to Schedule Your Pet's
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Appointment?
              </span>
            </h2>

            <p className="text-xl text-blue-100 leading-relaxed">
              Book now for convenient, stress-free veterinary care that puts your pet's comfort first
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                onClick={openAppointmentModal}
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <span>Book Online</span>
                <Calendar className="w-5 h-5 ml-2" />
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:0718376311" className="flex items-center space-x-2">
                  <Phone className="w-5 h-5" />
                  <span>Call 0718376311</span>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

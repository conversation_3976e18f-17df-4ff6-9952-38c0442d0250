'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Blog {
  id: number;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  image: string;
  category: string;
}

export default function BlogAdminPage() {
  const router = useRouter();
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [title, setTitle] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [content, setContent] = useState('');
  const [image, setImage] = useState('');
  const [category, setCategory] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Check if user is authenticated (simple check)
    const isAuthenticated = sessionStorage.getItem('blogAdminAuth');
    if (!isAuthenticated) {
      router.push('/');
      return;
    }

    // Load existing blogs
    const fetchBlogs = async () => {
      try {
        const response = await fetch('/data/blogs.json');
        const data = await response.json();
        setBlogs(data);
      } catch (error) {
        console.error('Error fetching blogs:', error);
      }
    };

    fetchBlogs();
  }, [router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const newBlog: Blog = {
        id: Math.max(...blogs.map(b => b.id), 0) + 1,
        title,
        excerpt,
        content,
        author: 'Admin',
        date: new Date().toISOString().split('T')[0],
        image: image || 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?auto=format&fit=crop&w=400&h=300',
        category: category || 'General'
      };

      // Sort blogs by date (newest first)
      const updatedBlogs = [newBlog, ...blogs].sort((a, b) => 
        new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      // In a real app, you'd send this to an API
      // For now, we'll just update the local state and show success
      setBlogs(updatedBlogs);
      
      // Reset form
      setTitle('');
      setExcerpt('');
      setContent('');
      setImage('');
      setCategory('');

      alert('Blog post created successfully! Note: In production, this would save to the server.');
    } catch (error) {
      console.error('Error creating blog:', error);
      alert('Error creating blog post');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    sessionStorage.removeItem('blogAdminAuth');
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-emerald-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <Button
            onClick={handleBack}
            variant="outline"
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Site
          </Button>
        </div>

        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-gray-800 flex items-center gap-2">
              <Plus className="w-6 h-6 text-cvets-primary" />
              Create New Blog Post
            </CardTitle>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter blog title"
                    required
                    className="border-gray-300 focus:border-cvets-primary focus:ring-cvets-primary"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    placeholder="e.g., Pet Care, Health Tips"
                    className="border-gray-300 focus:border-cvets-primary focus:ring-cvets-primary"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="image">Image URL</Label>
                <Input
                  id="image"
                  value={image}
                  onChange={(e) => setImage(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="border-gray-300 focus:border-cvets-primary focus:ring-cvets-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="excerpt">Excerpt *</Label>
                <Textarea
                  id="excerpt"
                  value={excerpt}
                  onChange={(e) => setExcerpt(e.target.value)}
                  placeholder="Brief description of the blog post"
                  required
                  rows={3}
                  className="border-gray-300 focus:border-cvets-primary focus:ring-cvets-primary"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Content *</Label>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your blog content here..."
                  required
                  rows={12}
                  className="border-gray-300 focus:border-cvets-primary focus:ring-cvets-primary"
                />
              </div>

              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-cvets-purple hover:bg-purple-700 text-white"
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Creating...' : 'Create Blog Post'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Recent blogs preview */}
        <Card className="mt-8 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-800">
              Recent Blog Posts ({blogs.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {blogs.slice(0, 5).map((blog) => (
                <div key={blog.id} className="border-l-4 border-cvets-primary pl-4 py-2">
                  <h3 className="font-semibold text-gray-800">{blog.title}</h3>
                  <p className="text-sm text-gray-600">{blog.date} • {blog.category}</p>
                  <p className="text-sm text-gray-700 mt-1">{blog.excerpt}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
